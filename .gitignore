# ESC Football App - .gitignore

# =============================================================================
# ENVIRONMENT & SECRETS
# =============================================================================
.env
.env.local
.env.development
.env.production
*.env
.envrc

# Secrets and keys
*.key
*.pem
*.p12
*.pfx
secrets/
config/secrets/

# =============================================================================
# BACKEND (Python/Flask)
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff (if used)
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff
instance/
.webassets-cache

# Scrapy stuff
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# FRONTEND (Angular/Node.js)
# =============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Angular specific
/dist/
/tmp/
/out-tsc/
/bazel-out/

# Angular dependencies
/node_modules/

# Angular profiling files
chrome-profiler-events*.json
speed-measure-plugin*.json

# =============================================================================
# DATABASE
# =============================================================================

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal

# Database dumps
*.sql
*.dump
backups/*.sql
backups/*.sql.gz
backups/*.dump

# Migration files (keep structure, ignore data)
migrations/versions/*.pyc

# =============================================================================
# DOCKER & CONTAINERS
# =============================================================================

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.local.yml

# Container volumes
postgres_data/
redis_data/
pgadmin_data/
backend_uploads/

# =============================================================================
# UPLOADS & MEDIA
# =============================================================================

# User uploads
uploads/
media/
static/uploads/
backend/uploads/*
!backend/uploads/.gitkeep

# Images
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
*.webp
*.ico

# Videos
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm

# Documents
*.pdf
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx

# Archives
*.zip
*.rar
*.7z
*.tar
*.tar.gz
*.tar.bz2

# =============================================================================
# LOGS
# =============================================================================

# Log files
*.log
logs/
log/
*.log.*
*.out

# Application logs
app.log
error.log
access.log
debug.log

# System logs
syslog
kern.log

# =============================================================================
# OPERATING SYSTEM
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# IDEs & EDITORS
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# PyCharm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# TESTING
# =============================================================================

# Test results
test-results/
test-reports/
coverage-reports/

# E2E testing
e2e/results/
e2e/screenshots/
e2e/videos/

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Monitoring data
prometheus_data/
grafana_data/

# Analytics
analytics/

# =============================================================================
# MISCELLANEOUS
# =============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig

# Temporary files
*.tmp
*.temp
*.swp
*.swo

# Lock files
*.lock
package-lock.json
yarn.lock

# Cache directories
.cache/
.npm/
.yarn/

# Runtime configuration
.runtime/

# Local configuration
local.json
local.yml
local.yaml

# SSL certificates
*.crt
*.csr

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Kubernetes
*.kubeconfig
