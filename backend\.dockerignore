__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

.DS_Store
.vscode
.idea

# Environment files
.env
.env.local
.env.development
.env.production

# Database
*.db
*.sqlite3

# Uploads
uploads/*
!uploads/.gitkeep

# Documentation
docs/
*.md
README.md

# Tests
tests/
test_*

# Development files
migrations/versions/*
!migrations/versions/.gitkeep
