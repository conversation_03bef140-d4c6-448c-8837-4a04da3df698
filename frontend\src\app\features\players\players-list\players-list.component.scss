// ESC Football - Players List Component Styles

.page-header {
  margin-bottom: 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    .header-title {
      h1 {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: #1976d2;

        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
      }

      .subtitle {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 0.9rem;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
}

.filters-card {
  margin-bottom: 24px;

  .filters-container {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;

    .search-field {
      min-width: 300px;
      flex: 1;
    }

    mat-form-field {
      min-width: 150px;
    }
  }
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #e3f2fd;
        color: #1976d2;

        &.active {
          background-color: #e8f5e8;
          color: #4caf50;
        }

        &.injured {
          background-color: #ffebee;
          color: #f44336;
        }

        &.average {
          background-color: #fff3e0;
          color: #ff9800;
        }

        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }
      }

      .stat-info {
        h3 {
          margin: 0;
          font-size: 2rem;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 4px 0 0 0;
          color: #666;
          font-size: 0.9rem;
        }
      }
    }
  }
}

.players-table-card {
  .table-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;

    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .table-container {
    overflow-x: auto;

    .players-table {
      width: 100%;

      .player-avatar {
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid #e0e0e0;
        }
      }

      .player-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background-color: #1976d2;
        color: white;
        border-radius: 50%;
        font-weight: 600;
        font-size: 0.9rem;
      }

      .player-name {
        strong {
          display: block;
          font-weight: 600;
        }

        small {
          color: #666;
          font-size: 0.8rem;
        }
      }

      // Position chips
      .position-gk {
        background-color: #ffeb3b;
        color: #333;
      }

      .position-def {
        background-color: #4caf50;
        color: white;
      }

      .position-mid {
        background-color: #2196f3;
        color: white;
      }

      .position-att {
        background-color: #f44336;
        color: white;
      }

      // Status chips
      .status-active {
        background-color: #4caf50;
        color: white;
      }

      .status-injured {
        background-color: #f44336;
        color: white;
      }

      .status-suspended {
        background-color: #ff9800;
        color: white;
      }

      .status-inactive {
        background-color: #9e9e9e;
        color: white;
      }

      .player-row {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      .delete-action {
        color: #f44336;
      }
    }
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;

    mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #ccc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #666;
    }

    p {
      margin: 0 0 24px 0;
      color: #999;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .page-header {
    .header-content {
      flex-direction: column;
      align-items: stretch;

      .header-actions {
        justify-content: center;
      }
    }
  }

  .filters-card {
    .filters-container {
      flex-direction: column;
      align-items: stretch;

      .search-field {
        min-width: auto;
      }

      mat-form-field {
        min-width: auto;
      }
    }
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .players-table-card {
    .table-container {
      .players-table {
        font-size: 0.8rem;

        .player-avatar img {
          width: 32px;
          height: 32px;
        }

        .player-number {
          width: 28px;
          height: 28px;
          font-size: 0.8rem;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-content {
      .header-title {
        h1 {
          font-size: 1.5rem;

          mat-icon {
            font-size: 1.5rem;
            width: 1.5rem;
            height: 1.5rem;
          }
        }
      }
    }
  }

  .stats-container {
    .stat-card {
      .stat-content {
        .stat-icon {
          width: 50px;
          height: 50px;

          mat-icon {
            font-size: 1.5rem;
            width: 1.5rem;
            height: 1.5rem;
          }
        }

        .stat-info {
          h3 {
            font-size: 1.5rem;
          }
        }
      }
    }
  }
}