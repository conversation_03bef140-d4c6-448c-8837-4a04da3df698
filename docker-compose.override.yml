# ESC Football App - Docker Compose Override pour les configurations locales
# Ce fichier est automatiquement utilisé par docker-compose
# Espoir Sportif de Chorbane

version: '3.8'

services:
  # =============================================================================
  # OVERRIDES POUR LE DÉVELOPPEMENT LOCAL
  # =============================================================================

  # Configuration locale pour la base de données
  db:
    environment:
      # Ajout de configurations spécifiques au développement local
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      # Ajout de scripts de développement
      - ./database/dev-data.sql:/docker-entrypoint-initdb.d/03-dev-data.sql
    # Exposition du port pour accès direct depuis l'hôte
    ports:
      - "5432:5432"

  # Configuration locale pour Redis
  redis:
    # Exposition du port pour accès direct depuis l'hôte
    ports:
      - "6379:6379"
    # Configuration pour le développement (pas de mot de passe)
    command: redis-server --appendonly yes

  # Configuration locale pour le backend
  backend:
    environment:
      # Variables d'environnement pour le développement local
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - PYTHONPATH=/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    volumes:
      # Montage du code source pour le hot reload
      - ./backend:/app:cached
      - ./backend/logs:/app/logs
      # Cache pip pour accélérer les rebuilds
      - pip_cache:/root/.cache/pip
    # Exposition de ports supplémentaires pour le debugging
    ports:
      - "5000:5000"
      - "5678:5678"  # Port pour le debugger Python (ptvsd/debugpy)
    # Activation du mode interactif
    stdin_open: true
    tty: true

  # Configuration locale pour le frontend
  frontend:
    environment:
      # Variables d'environnement pour le développement local
      - NODE_ENV=development
      - NG_CLI_ANALYTICS=false
      - CHOKIDAR_USEPOLLING=true
      - CHOKIDAR_INTERVAL=1000
    volumes:
      # Montage du code source pour le hot reload
      - ./frontend:/app:cached
      - /app/node_modules
      # Cache npm pour accélérer les rebuilds
      - npm_cache:/root/.npm
    # Exposition de ports supplémentaires pour le live reload
    ports:
      - "4200:4200"
      - "49153:49153"  # Port pour le live reload d'Angular
    # Activation du mode interactif
    stdin_open: true
    tty: true

  # Configuration locale pour pgAdmin
  pgadmin:
    environment:
      # Configuration simplifiée pour le développement
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
      - PGADMIN_CONFIG_ENHANCED_COOKIE_PROTECTION=False
    volumes:
      # Configuration des serveurs pré-configurés
      - ./pgadmin/servers-local.json:/pgadmin4/servers.json
    # Toujours actif en développement
    profiles: []

  # Configuration locale pour Redis Commander
  redis-commander:
    environment:
      # Configuration simplifiée pour le développement
      - REDIS_HOSTS=local:redis:6379:0
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    # Toujours actif en développement
    profiles: []

  # Configuration locale pour Mailhog
  mailhog:
    # Toujours actif en développement
    profiles: []

# =============================================================================
# VOLUMES SUPPLÉMENTAIRES POUR LE DÉVELOPPEMENT
# =============================================================================

volumes:
  # Cache pour pip (Python)
  pip_cache:
    driver: local
    name: esc_pip_cache

  # Cache pour npm (Node.js)
  npm_cache:
    driver: local
    name: esc_npm_cache

  # Logs de développement
  dev_logs:
    driver: local
    name: esc_dev_logs

# =============================================================================
# CONFIGURATION RÉSEAU POUR LE DÉVELOPPEMENT
# =============================================================================

networks:
  esc_network:
    # Configuration étendue pour le développement
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
