/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AOA: never[];
    ARS: never[];
    AUD: never[];
    BAM: never[];
    BBD: never[];
    BDT: never[];
    BMD: never[];
    BND: never[];
    BOB: never[];
    BRL: never[];
    BSD: never[];
    BWP: never[];
    BZD: never[];
    CAD: never[];
    CLP: never[];
    CNY: never[];
    COP: never[];
    CRC: never[];
    CUC: never[];
    CUP: never[];
    CZK: never[];
    DKK: never[];
    DOP: never[];
    EGP: never[];
    ESP: never[];
    FIM: string[];
    FJD: never[];
    FKP: never[];
    GEL: never[];
    GIP: never[];
    GNF: never[];
    GTQ: never[];
    GYD: never[];
    HKD: never[];
    HNL: never[];
    HRK: never[];
    HUF: never[];
    IDR: never[];
    ILS: never[];
    INR: never[];
    ISK: never[];
    JMD: never[];
    KHR: never[];
    KMF: never[];
    KPW: never[];
    KRW: never[];
    KYD: never[];
    KZT: never[];
    LAK: never[];
    LBP: never[];
    LKR: never[];
    LRD: never[];
    LTL: never[];
    LVL: never[];
    MGA: never[];
    MMK: never[];
    MNT: never[];
    MUR: never[];
    MXN: never[];
    MYR: never[];
    NAD: never[];
    NGN: never[];
    NIO: never[];
    NOK: never[];
    NPR: never[];
    NZD: never[];
    PHP: never[];
    PKR: never[];
    PLN: never[];
    PYG: never[];
    RON: never[];
    RWF: never[];
    SBD: never[];
    SEK: never[];
    SGD: never[];
    SHP: never[];
    SRD: never[];
    SSP: never[];
    STN: (string | undefined)[];
    SYP: never[];
    THB: never[];
    TOP: never[];
    TRY: never[];
    TTD: never[];
    TWD: never[];
    UAH: never[];
    UYU: never[];
    VEF: never[];
    VND: never[];
    XCD: never[];
    XPF: never[];
    XXX: never[];
    ZAR: never[];
    ZMW: never[];
} | undefined)[];
export default _default;
