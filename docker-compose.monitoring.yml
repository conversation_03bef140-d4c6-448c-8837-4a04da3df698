# ESC Football App - Docker Compose pour le monitoring
# Espoir Sportif de Chorbane

version: '3.8'

services:
  # =============================================================================
  # SERVICES DE MONITORING
  # =============================================================================

  # Prometheus pour la collecte de métriques
  prometheus:
    image: prom/prometheus:latest
    container_name: esc_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    networks:
      - esc_monitoring_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Grafana pour la visualisation des métriques
  grafana:
    image: grafana/grafana:latest
    container_name: esc_grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - esc_monitoring_network
    depends_on:
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Node Exporter pour les métriques système
  node-exporter:
    image: prom/node-exporter:latest
    container_name: esc_node_exporter
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - esc_monitoring_network
    restart: unless-stopped

  # cAdvisor pour les métriques des conteneurs
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: esc_cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - esc_monitoring_network
    restart: unless-stopped
    privileged: true
    devices:
      - /dev/kmsg

  # AlertManager pour la gestion des alertes
  alertmanager:
    image: prom/alertmanager:latest
    container_name: esc_alertmanager
    command:
      - '--config.file=/etc/alertmanager/config.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/config.yml:/etc/alertmanager/config.yml
      - alertmanager_data:/alertmanager
    networks:
      - esc_monitoring_network
    restart: unless-stopped

  # Loki pour la collecte de logs
  loki:
    image: grafana/loki:latest
    container_name: esc_loki
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki/loki-config.yaml:/etc/loki/local-config.yaml
      - loki_data:/loki
    networks:
      - esc_monitoring_network
    restart: unless-stopped

  # Promtail pour l'envoi de logs vers Loki
  promtail:
    image: grafana/promtail:latest
    container_name: esc_promtail
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./monitoring/promtail/config.yml:/etc/promtail/config.yml
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    networks:
      - esc_monitoring_network
    depends_on:
      - loki
    restart: unless-stopped

  # Jaeger pour le tracing distribué
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: esc_jaeger
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "9411:9411"
    networks:
      - esc_monitoring_network
    restart: unless-stopped

  # Elasticsearch pour le stockage des logs (optionnel)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: esc_elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - esc_monitoring_network
    restart: unless-stopped
    profiles:
      - elk

  # Kibana pour la visualisation des logs (optionnel)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: esc_kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - esc_monitoring_network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - elk

  # Uptime Kuma pour le monitoring de disponibilité
  uptime-kuma:
    image: louislam/uptime-kuma:latest
    container_name: esc_uptime_kuma
    ports:
      - "3001:3001"
    volumes:
      - uptime_kuma_data:/app/data
    networks:
      - esc_monitoring_network
    restart: unless-stopped

volumes:
  prometheus_data:
    driver: local
    name: esc_prometheus_data
  grafana_data:
    driver: local
    name: esc_grafana_data
  alertmanager_data:
    driver: local
    name: esc_alertmanager_data
  loki_data:
    driver: local
    name: esc_loki_data
  elasticsearch_data:
    driver: local
    name: esc_elasticsearch_data
  uptime_kuma_data:
    driver: local
    name: esc_uptime_kuma_data

networks:
  esc_monitoring_network:
    driver: bridge
    name: esc_monitoring_network
    ipam:
      config:
        - subnet: **********/16
