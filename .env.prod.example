# ESC Football App - Variables d'environnement pour la production
# Espoir Sportif de Chorbane
# 
# IMPORTANT: Copiez ce fichier vers .env.prod et modifiez les valeurs
# Ne commitez JAMAIS le fichier .env.prod avec les vraies valeurs !

# =============================================================================
# CONFIGURATION DE BASE
# =============================================================================

# Environnement
COMPOSE_PROJECT_NAME=esc_football_prod
COMPOSE_FILE=docker-compose.yml:docker-compose.prod.yml

# =============================================================================
# BASE DE DONNÉES POSTGRESQL
# =============================================================================

POSTGRES_DB=esc_db
POSTGRES_USER=esc_user
POSTGRES_PASSWORD=CHANGEZ_MOI_AVEC_UN_MOT_DE_PASSE_FORT

# =============================================================================
# REDIS
# =============================================================================

REDIS_PASSWORD=CHANGEZ_MOI_AVEC_UN_MOT_DE_PASSE_FORT

# =============================================================================
# FLASK BACKEND
# =============================================================================

# Clés secrètes (générez des clés uniques et fortes)
SECRET_KEY=CHANGEZ_MOI_AVEC_UNE_CLE_SECRETE_FORTE_ET_UNIQUE
JWT_SECRET_KEY=CHANGEZ_MOI_AVEC_UNE_CLE_JWT_FORTE_ET_UNIQUE

# CORS (domaines autorisés)
CORS_ORIGINS=https://esc.tn,https://www.esc.tn,https://app.esc.tn

# =============================================================================
# CONFIGURATION EMAIL
# =============================================================================

# Serveur SMTP
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=1
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=votre-mot-de-passe-app

# Email de l'expéditeur par défaut
MAIL_DEFAULT_SENDER=<EMAIL>

# =============================================================================
# CONFIGURATION SSL/TLS
# =============================================================================

# Domaine principal
DOMAIN=esc.tn
WWW_DOMAIN=www.esc.tn

# Certificats SSL (Let's Encrypt)
SSL_EMAIL=<EMAIL>

# =============================================================================
# CONFIGURATION NGINX
# =============================================================================

# Taille maximale des uploads
CLIENT_MAX_BODY_SIZE=50M

# =============================================================================
# CONFIGURATION DE SAUVEGARDE
# =============================================================================

# Rétention des sauvegardes (en jours)
BACKUP_RETENTION_DAYS=30

# Fréquence des sauvegardes (cron format)
BACKUP_SCHEDULE=0 2 * * *

# =============================================================================
# CONFIGURATION MONITORING
# =============================================================================

# Grafana
GRAFANA_ADMIN_PASSWORD=CHANGEZ_MOI_AVEC_UN_MOT_DE_PASSE_FORT

# Prometheus
PROMETHEUS_RETENTION=30d

# =============================================================================
# CONFIGURATION EXTERNE
# =============================================================================

# API Keys externes (si nécessaire)
GOOGLE_MAPS_API_KEY=votre-cle-google-maps
FACEBOOK_APP_ID=votre-app-id-facebook
TWITTER_API_KEY=votre-cle-twitter

# =============================================================================
# CONFIGURATION DE SÉCURITÉ
# =============================================================================

# Rate limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Session
SESSION_TIMEOUT=3600

# =============================================================================
# CONFIGURATION DE PERFORMANCE
# =============================================================================

# Workers Gunicorn
GUNICORN_WORKERS=4
GUNICORN_THREADS=2

# Workers Celery
CELERY_WORKERS=4

# =============================================================================
# CONFIGURATION DE LOGGING
# =============================================================================

# Niveau de log
LOG_LEVEL=INFO

# Rétention des logs (en jours)
LOG_RETENTION_DAYS=30

# =============================================================================
# CONFIGURATION DE CACHE
# =============================================================================

# TTL par défaut du cache (en secondes)
CACHE_DEFAULT_TTL=3600

# =============================================================================
# CONFIGURATION DE DÉVELOPPEMENT (à supprimer en production)
# =============================================================================

# Debug (TOUJOURS False en production)
FLASK_DEBUG=0
DEBUG=0

# =============================================================================
# VARIABLES SPÉCIFIQUES À L'APPLICATION
# =============================================================================

# Nom du club
CLUB_NAME=Espoir Sportif de Chorbane
CLUB_SHORT_NAME=ESC

# Saison actuelle
CURRENT_SEASON=2024-2025

# Devise par défaut
DEFAULT_CURRENCY=TND

# Langue par défaut
DEFAULT_LANGUAGE=fr

# Fuseau horaire
TIMEZONE=Africa/Tunis

# =============================================================================
# CONFIGURATION SOCIALE
# =============================================================================

# URLs des réseaux sociaux
FACEBOOK_URL=https://facebook.com/esc.chorbane
TWITTER_URL=https://twitter.com/esc_chorbane
INSTAGRAM_URL=https://instagram.com/esc_chorbane
YOUTUBE_URL=https://youtube.com/c/esc-chorbane

# =============================================================================
# CONFIGURATION DE CONTACT
# =============================================================================

# Informations de contact
CONTACT_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# Téléphone
CONTACT_PHONE=+216 XX XXX XXX

# Adresse
CLUB_ADDRESS=Chorbane, Mahdia, Tunisie

# =============================================================================
# NOTES IMPORTANTES
# =============================================================================

# 1. Générez des mots de passe forts pour toutes les variables *_PASSWORD
# 2. Générez des clés secrètes uniques pour SECRET_KEY et JWT_SECRET_KEY
# 3. Configurez correctement les domaines dans CORS_ORIGINS
# 4. Assurez-vous que les certificats SSL sont correctement configurés
# 5. Testez la configuration email avant le déploiement
# 6. Configurez les sauvegardes automatiques
# 7. Mettez en place le monitoring et les alertes
# 8. Documentez tous les changements de configuration
