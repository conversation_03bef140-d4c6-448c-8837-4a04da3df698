/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* ESC Football App - Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Inter+Tight:wght@400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    @apply bg-gray-50 text-gray-900;
  }

  * {
    box-sizing: border-box;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter Tight', 'Inter', sans-serif;
    font-weight: 600;
    line-height: 1.2;
  }

  h1 { @apply text-3xl lg:text-4xl; }
  h2 { @apply text-2xl lg:text-3xl; }
  h3 { @apply text-xl lg:text-2xl; }
  h4 { @apply text-lg lg:text-xl; }
  h5 { @apply text-base lg:text-lg; }
  h6 { @apply text-sm lg:text-base; }

  p {
    @apply leading-relaxed;
  }

  a {
    @apply transition-colors duration-200;
  }

  button {
    @apply transition-all duration-200;
  }

  input, textarea, select {
    @apply transition-all duration-200;
  }
}

/* Component styles */
@layer components {
  /* ESC Button Styles */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-success {
    @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2;
  }

  .btn-warning {
    @apply bg-warning-600 hover:bg-warning-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2;
  }

  .btn-error {
    @apply bg-error-600 hover:bg-error-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Card Styles */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-100 bg-gray-50;
  }

  /* Form Styles */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-sm text-error-600 mt-1;
  }

  /* Badge Styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply bg-error-100 text-error-800;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }

  /* Status Badges */
  .status-active {
    @apply badge badge-success;
  }

  .status-inactive {
    @apply badge badge-gray;
  }

  .status-injured {
    @apply badge badge-error;
  }

  .status-suspended {
    @apply badge badge-warning;
  }

  /* Position Badges */
  .position-gk {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .position-def {
    @apply badge bg-green-100 text-green-800;
  }

  .position-mid {
    @apply badge bg-blue-100 text-blue-800;
  }

  .position-att {
    @apply badge bg-red-100 text-red-800;
  }

  /* Navigation Styles */
  .nav-link {
    @apply flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-lg transition-all duration-200;
  }

  .nav-link.active {
    @apply bg-primary-100 text-primary-700 font-medium;
  }

  /* Table Styles */
  .table {
    @apply w-full divide-y divide-gray-200;
  }

  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
  }

  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  .table tr:hover {
    @apply bg-gray-50;
  }

  /* Loading Styles */
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
  }

  /* Page Header */
  .page-header {
    @apply mb-8;
  }

  .page-title {
    @apply text-2xl font-bold text-gray-900 mb-2;
  }

  .page-subtitle {
    @apply text-gray-600;
  }

  /* Stats Card */
  .stat-card {
    @apply card p-6;
  }

  .stat-value {
    @apply text-3xl font-bold text-gray-900;
  }

  .stat-label {
    @apply text-sm text-gray-600 mt-1;
  }

  .stat-icon {
    @apply w-12 h-12 rounded-lg flex items-center justify-center;
  }
}

/* Utility styles */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.primary.800'));
  }

  .gradient-success {
    background: linear-gradient(135deg, theme('colors.success.500'), theme('colors.success.700'));
  }

  .gradient-warning {
    background: linear-gradient(135deg, theme('colors.warning.500'), theme('colors.warning.700'));
  }

  .gradient-error {
    background: linear-gradient(135deg, theme('colors.error.500'), theme('colors.error.700'));
  }
}

/* ESC Football specific styles */
.esc-logo {
  @apply w-8 h-8 rounded-full bg-primary-600 flex items-center justify-center text-white font-bold text-sm;
}

.player-avatar {
  @apply w-10 h-10 rounded-full object-cover border-2 border-gray-200;
}

.player-number {
  @apply w-8 h-8 rounded-full bg-primary-600 text-white flex items-center justify-center text-sm font-bold;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 641px) {
  .mobile-only {
    display: none;
  }
}
