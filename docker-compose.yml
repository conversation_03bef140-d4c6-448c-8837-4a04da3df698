version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: esc_db
    environment:
      POSTGRES_DB: esc_db
      POSTGRES_USER: esc_user
      POSTGRES_PASSWORD: esc_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - esc_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U esc_user -d esc_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and Celery
  redis:
    image: redis:7-alpine
    container_name: esc_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - esc_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flask Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: esc_backend
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=******************************************/esc_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
      - CORS_ORIGINS=http://localhost:4200,http://localhost:3000
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - esc_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Angular Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: esc_frontend
    environment:
      - NODE_ENV=development
      - API_URL=http://localhost:4200/api
    ports:
      - "4200:4200"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - esc_network
    restart: unless-stopped

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: esc_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - backend_uploads:/var/www/uploads
    depends_on:
      - backend
      - frontend
    networks:
      - esc_network
    restart: unless-stopped
    profiles:
      - production

  # Celery Worker (for background tasks)
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: esc_celery
    command: celery -A app.celery worker --loglevel=info
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=******************************************/esc_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - esc_network
    restart: unless-stopped
    profiles:
      - production

  # Celery Beat (for scheduled tasks)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: esc_celery_beat
    command: celery -A app.celery beat --loglevel=info
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=******************************************/esc_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend:/app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - esc_network
    restart: unless-stopped
    profiles:
      - production

  # pgAdmin (for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: esc_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./pgadmin/servers.json:/pgadmin4/servers.json
    depends_on:
      - db
    networks:
      - esc_network
    restart: unless-stopped
    profiles:
      - development
      - tools

  # Redis Commander (for Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: esc_redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379:0
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - esc_network
    restart: unless-stopped
    profiles:
      - development
      - tools

  # Mailhog (for email testing in development)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: esc_mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - esc_network
    restart: unless-stopped
    profiles:
      - development
      - tools

volumes:
  postgres_data:
    driver: local
    name: esc_postgres_data
  redis_data:
    driver: local
    name: esc_redis_data
  backend_uploads:
    driver: local
    name: esc_backend_uploads
  pgadmin_data:
    driver: local
    name: esc_pgadmin_data
  backend_logs:
    driver: local
    name: esc_backend_logs
  frontend_dist:
    driver: local
    name: esc_frontend_dist

networks:
  esc_network:
    driver: bridge
