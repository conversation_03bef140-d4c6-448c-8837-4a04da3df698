.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 20px;
}

.login-card-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  padding: 0;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
}

.logo {
  margin-bottom: 16px;
}

.logo-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid white;
  background: white;
  padding: 8px;
}

.title-section {
  .mat-mdc-card-title {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .mat-mdc-card-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
  }
}

.login-form {
  padding: 32px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.full-width {
  width: 100%;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f44336;
  background: rgba(244, 67, 54, 0.1);
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid #f44336;
  font-size: 14px;

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
  border-radius: 8px;

  .loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.footer-content {
  text-align: center;
  padding: 16px 24px;
  background: #f5f5f5;
  border-top: 1px solid #e0e0e0;

  .version-info {
    margin: 0 0 4px 0;
    font-size: 12px;
    color: #666;
  }

  .copyright {
    margin: 0;
    font-size: 12px;
    color: #999;
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-form {
    padding: 24px 20px 20px;
  }

  .header-content {
    padding: 20px;
  }

  .logo-img {
    width: 50px;
    height: 50px;
  }

  .title-section {
    .mat-mdc-card-title {
      font-size: 20px;
    }
  }
}

// Material form field customization
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: 8px;
  }

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }
}

// Button hover effects
.login-button:not(:disabled) {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}